import { initRequest } from "../request";
import { WalletType } from "@/services/store/store";
import { getProviderAndSigner } from "@/utils";
import axios from "axios";
import { createL2Headers } from "pedone-clob-client";

// API 基础 URL
const INVITE_API_BASE = (process.env.NEXT_PUBLIC_MYSTERY_BOX_API || "http://localhost:5201")
  .replace(/\/api\/mystery-box$/, "") // 移除 /api/mystery-box 后缀
  .replace(/\/api$/, ""); // 移除 /api 后缀

// 邀请码相关接口类型定义
export interface GetOrCreateInvitationRequest {
  proxy_wallet: string;
}

export interface GetOrCreateInvitationResponse {
  success: boolean;
  message?: string;
  data: {
    invitation_code: string;
  };
}

export async function getWebUsersByProxyWallet(proxy_wallet: string) {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/backGetWebUser`,
    method: "get",
    headers: { "Content-Type": "application/json" },
    params: {
      proxy_wallet: proxy_wallet,
    },
  });
}

export async function getCheckInviteDuplicate(invitation_code: string) {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/backCheckInviteDuplicate?invitation_code=${invitation_code}`,
    method: "get",
    headers: { "Content-Type": "application/json" },
  });
}

export async function getRewardDistributionByProxyWallet(proxy_wallet: string) {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getRewardDistribution`,
    method: "get",
    headers: { "Content-Type": "application/json" },
    params: {
      proxy_wallet: proxy_wallet,
    },
  });
}

export async function getUserRewards(proxy_wallet: string) {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getUserRewards`,
    method: "get",
    headers: { "Content-Type": "application/json" },
    params: {
      proxy_wallet: proxy_wallet,
    },
  });
}

export async function claimAllRewards(creds: any, walletType: WalletType) {
  const signer = await getProviderAndSigner(walletType);
  if (!signer) {
    throw new Error("Signer is undefined");
  }

  const headerArgs = {
    method: "GET",
    requestPath: "/offchain-claim-reward",
  };
  const address = await signer.getAddress();

  // @ts-ignore
  const l2Headers = await createL2Headers(signer, creds[address], headerArgs);

  return initRequest({
    url: `${process.env.NEXT_PUBLIC_PDONE_ENDPOINT}/offchain-claim-reward`,
    method: "get",
    headers: {
      ...l2Headers,
    },
    params: {
      token: "usdc",
    },
  });
}

export async function getClaimReward(creds: any, walletType: WalletType) {
  const signer = await getProviderAndSigner(walletType);
  if (!signer) {
    throw new Error("Signer is undefined");
  }

  const headerArgs = {
    method: "GET",
    requestPath: "/claim-reward",
  };
  const address = await signer.getAddress();

  // @ts-ignore
  const l2Headers = await createL2Headers(signer, creds[address], headerArgs);

  return initRequest({
    url: `${process.env.NEXT_PUBLIC_PDONE_ENDPOINT}/claim-reward`,
    method: "get",
    headers: {
      ...l2Headers,
    },
  });
}

export async function getInvitedUsersByCode(invitation_code: string) {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getUsersByInvitationCode`,
    method: "get",
    headers: { "Content-Type": "application/json" },
    params: {
      invitation_code: invitation_code,
    },
  });
}

export async function getMysteryboxByProxyWallet(proxy_wallet: string) {
  return initRequest({
    url: `${process.env.NEXT_PUBLIC_HASURA_ENDPOINT}/getMysteryboxByProxyWallet`,
    method: "get",
    headers: { "Content-Type": "application/json" },
    params: {
      proxy_wallet: proxy_wallet,
    },
  });
}

/**
 * 获取或创建邀请码
 */
export const getOrCreateInvitationCode = async (
  data: GetOrCreateInvitationRequest,
): Promise<GetOrCreateInvitationResponse> => {
  try {
    const response = await axios.post(`${INVITE_API_BASE}/api/invitation/get-or-create`, data, {
      timeout: 30000,
      headers: {
        "Content-Type": "application/json",
      },
    });
    return response.data;
  } catch (error: any) {
    console.error("Invitation API error:", error);

    // 提供更详细的错误信息
    if (error.code === "ECONNABORTED") {
      throw new Error("获取邀请码请求超时，请检查网络连接后重试");
    } else if (error.response?.status === 400) {
      throw new Error(error.response.data?.message || "请求参数错误");
    } else if (error.response?.status === 404) {
      throw new Error("用户不存在，请先激活账户");
    } else if (error.response?.status >= 500) {
      throw new Error("服务器错误，请稍后重试");
    } else {
      throw new Error(error.message || "获取邀请码失败，请重试");
    }
  }
};
